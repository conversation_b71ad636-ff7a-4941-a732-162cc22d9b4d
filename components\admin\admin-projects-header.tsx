"use client"

import { useState } from "react"

import {
  RiRocketLine,
  RiAddLine,
  RiDownloadLine,
  RiRefreshLine,
  RiFilterLine,
} from "@remixicon/react"

import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

export function AdminProjectsHeader() {
  const [isRefreshing, setIsRefreshing] = useState(false)

  const handleRefresh = async () => {
    setIsRefreshing(true)
    // Simulate refresh
    await new Promise((resolve) => setTimeout(resolve, 1000))
    window.location.reload()
  }

  const handleExport = () => {
    // TODO: Implement export functionality
    console.log("Exporting projects...")
  }

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      {/* Title and description */}
      <div className="space-y-1">
        <div className="flex items-center gap-2">
          <RiRocketLine className="h-6 w-6 text-primary" />
          <h1 className="text-2xl font-bold tracking-tight">Quản lý Projects</h1>
          <Badge variant="secondary" className="ml-2">
            Admin
          </Badge>
        </div>
        <p className="text-muted-foreground">
          Quản lý tất cả các dự án trong hệ thống. Chỉnh sửa, xóa, và thay đổi trạng thái projects.
        </p>
      </div>

      {/* Actions */}
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
          className="gap-2"
        >
          <RiRefreshLine className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
          Làm mới
        </Button>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="gap-2">
              <RiDownloadLine className="h-4 w-4" />
              Xuất dữ liệu
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem onClick={handleExport}>
              Xuất CSV
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleExport}>
              Xuất Excel
            </DropdownMenuItem>
            <DropdownMenuItem onClick={handleExport}>
              Xuất JSON
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        <Button size="sm" className="gap-2">
          <RiAddLine className="h-4 w-4" />
          Thêm Project
        </Button>
      </div>
    </div>
  )
}
