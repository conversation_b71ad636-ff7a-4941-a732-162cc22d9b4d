"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"

import {
  RiCloseLine,
  RiDeleteBinLine,
  RiStarLine,
  RiRocketLine,
} from "@remixicon/react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { bulkDeleteProjects, bulkUpdateProjects } from "@/app/actions/admin"
import { toast } from "sonner"

interface AdminBulkActionsProps {
  selectedProjects: string[]
  onClearSelection: () => void
}

export function AdminBulkActions({ selectedProjects, onClearSelection }: AdminBulkActionsProps) {
  const router = useRouter()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [isProcessing, setIsProcessing] = useState(false)

  const handleBulkDelete = async () => {
    setIsProcessing(true)
    try {
      const result = await bulkDeleteProjects(selectedProjects)
      if (result.success) {
        toast.success(`Đã xóa ${selectedProjects.length} projects thành công`)
        onClearSelection()
        router.refresh()
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi xóa projects")
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa projects")
    } finally {
      setIsProcessing(false)
      setDeleteDialogOpen(false)
    }
  }

  const handleBulkUpdate = async (updates: {
    launchStatus?: string
    featuredOnHomepage?: boolean
    launchType?: string
  }) => {
    setIsProcessing(true)
    try {
      const result = await bulkUpdateProjects(selectedProjects, updates)
      if (result.success) {
        toast.success(`Đã cập nhật ${selectedProjects.length} projects thành công`)
        router.refresh()
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi cập nhật projects")
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi cập nhật projects")
    } finally {
      setIsProcessing(false)
    }
  }

  return (
    <div className="flex items-center gap-2 rounded-lg border bg-card p-2">
      <div className="flex-1 px-2">
        <span className="font-medium">
          Đã chọn {selectedProjects.length} projects
        </span>
      </div>

      <div className="flex items-center gap-2">
        {/* Status dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" disabled={isProcessing}>
              <RiRocketLine className="mr-2 h-4 w-4" />
              Trạng thái
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => handleBulkUpdate({ launchStatus: "scheduled" })}
            >
              Đã lên lịch
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleBulkUpdate({ launchStatus: "launched" })}
            >
              Đã ra mắt
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleBulkUpdate({ launchStatus: "draft" })}
            >
              Bản nháp
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleBulkUpdate({ launchStatus: "cancelled" })}
            >
              Đã hủy
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Featured dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" disabled={isProcessing}>
              <RiStarLine className="mr-2 h-4 w-4" />
              Featured
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuItem
              onClick={() => handleBulkUpdate({ featuredOnHomepage: true })}
            >
              Đánh dấu featured
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => handleBulkUpdate({ featuredOnHomepage: false })}
            >
              Bỏ đánh dấu featured
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Delete button */}
        <Button
          variant="destructive"
          size="sm"
          onClick={() => setDeleteDialogOpen(true)}
          disabled={isProcessing}
        >
          <RiDeleteBinLine className="mr-2 h-4 w-4" />
          Xóa
        </Button>

        {/* Clear selection */}
        <Button
          variant="ghost"
          size="sm"
          onClick={onClearSelection}
          disabled={isProcessing}
        >
          <RiCloseLine className="mr-2 h-4 w-4" />
          Bỏ chọn
        </Button>
      </div>

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa nhiều projects</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa {selectedProjects.length} projects đã chọn? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleBulkDelete}
              disabled={isProcessing}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isProcessing ? "Đang xóa..." : "Xóa"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
