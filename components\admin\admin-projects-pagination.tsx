"use client"

import { useRouter, useSearchParams } from "next/navigation"

import {
  RiArrowLeftLine,
  RiArrowRightLine,
  RiMoreLine,
} from "@remixicon/react"

import { Button } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface AdminProjectsPaginationProps {
  totalPages: number
  currentPage: number
  totalCount: number
  currentFilters: {
    search: string
    status: string
    launchType: string
    category: string
    sortBy: string
    sortOrder: string
  }
}

export function AdminProjectsPagination({
  totalPages,
  currentPage,
  totalCount,
  currentFilters,
}: AdminProjectsPaginationProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const navigateToPage = (page: number) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set("page", page.toString())
    router.push(`/admin/projects?${params.toString()}`)
  }

  const changePageSize = (pageSize: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set("limit", pageSize)
    params.delete("page") // Reset to first page
    router.push(`/admin/projects?${params.toString()}`)
  }

  // Calculate pagination range
  const getPageNumbers = () => {
    const delta = 2
    const range = []
    const rangeWithDots = []

    for (
      let i = Math.max(2, currentPage - delta);
      i <= Math.min(totalPages - 1, currentPage + delta);
      i++
    ) {
      range.push(i)
    }

    if (currentPage - delta > 2) {
      rangeWithDots.push(1, "...")
    } else {
      rangeWithDots.push(1)
    }

    rangeWithDots.push(...range)

    if (currentPage + delta < totalPages - 1) {
      rangeWithDots.push("...", totalPages)
    } else if (totalPages > 1) {
      rangeWithDots.push(totalPages)
    }

    return rangeWithDots
  }

  if (totalPages <= 1) {
    return (
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Hiển thị {totalCount} kết quả
        </div>
      </div>
    )
  }

  const pageNumbers = getPageNumbers()
  const startItem = (currentPage - 1) * 10 + 1
  const endItem = Math.min(currentPage * 10, totalCount)

  return (
    <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
      {/* Results info */}
      <div className="text-sm text-muted-foreground">
        Hiển thị {startItem}-{endItem} trong tổng số {totalCount} kết quả
      </div>

      {/* Pagination controls */}
      <div className="flex items-center gap-2">
        {/* Page size selector */}
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Hiển thị:</span>
          <Select defaultValue="10" onValueChange={changePageSize}>
            <SelectTrigger className="w-20">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="5">5</SelectItem>
              <SelectItem value="10">10</SelectItem>
              <SelectItem value="20">20</SelectItem>
              <SelectItem value="50">50</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Page navigation */}
        <div className="flex items-center gap-1">
          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateToPage(currentPage - 1)}
            disabled={currentPage <= 1}
            className="h-8 w-8 p-0"
          >
            <RiArrowLeftLine className="h-4 w-4" />
          </Button>

          {pageNumbers.map((pageNumber, index) => {
            if (pageNumber === "...") {
              return (
                <div
                  key={`dots-${index}`}
                  className="flex h-8 w-8 items-center justify-center"
                >
                  <RiMoreLine className="h-4 w-4 text-muted-foreground" />
                </div>
              )
            }

            const page = pageNumber as number
            const isCurrentPage = page === currentPage

            return (
              <Button
                key={page}
                variant={isCurrentPage ? "default" : "outline"}
                size="sm"
                onClick={() => navigateToPage(page)}
                className="h-8 w-8 p-0"
              >
                {page}
              </Button>
            )
          })}

          <Button
            variant="outline"
            size="sm"
            onClick={() => navigateToPage(currentPage + 1)}
            disabled={currentPage >= totalPages}
            className="h-8 w-8 p-0"
          >
            <RiArrowRightLine className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  )
}
