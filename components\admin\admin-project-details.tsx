"use client"

import { useState } from "react"
import Image from "next/image"
import Link from "next/link"
import { useRouter } from "next/navigation"

import {
  RiArrowLeftLine,
  RiEditLine,
  RiDeleteBinLine,
  RiExternalLinkLine,
  RiStarLine,
  RiStarFill,
  RiCalendarLine,
  RiUserLine,
  RiPriceTagLine,
  RiCodeLine,
  RiSmartphoneLine,
  RiMoneyDollarCircleLine,
  RiGithubLine,
  RiTwitterLine,
} from "@remixicon/react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Separator } from "@/components/ui/separator"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  <PERSON><PERSON><PERSON><PERSON>ogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"
import { deleteAdminProject, toggleProjectFeatured } from "@/app/actions/admin"
import { toast } from "sonner"

interface Project {
  id: string
  name: string
  slug: string
  description: string
  websiteUrl: string
  logoUrl: string
  coverImageUrl: string | null
  productImage: string | null
  githubUrl: string | null
  twitterUrl: string | null
  techStack: string[] | null
  pricing: string
  platforms: string[] | null
  launchStatus: string
  scheduledLaunchDate: Date | null
  launchType: string | null
  featuredOnHomepage: boolean
  dailyRanking: number | null
  createdAt: Date
  updatedAt: Date
  createdBy: string | null
  categories: { id: string; name: string }[]
}

interface AdminProjectDetailsProps {
  project: Project
}

const statusLabels = {
  scheduled: "Đã lên lịch",
  launched: "Đã ra mắt",
  draft: "Bản nháp",
  cancelled: "Đã hủy",
}

const statusVariants = {
  scheduled: "default",
  launched: "default",
  draft: "secondary",
  cancelled: "destructive",
} as const

const launchTypeLabels = {
  free: "Miễn phí",
  premium: "Premium",
  premium_plus: "Premium Plus",
}

const pricingLabels = {
  FREE: "Miễn phí",
  FREEMIUM: "Freemium",
  PAID: "Trả phí",
}

export function AdminProjectDetails({ project }: AdminProjectDetailsProps) {
  const router = useRouter()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [isDeleting, setIsDeleting] = useState(false)

  const handleDeleteProject = async () => {
    setIsDeleting(true)
    try {
      const result = await deleteAdminProject(project.id)
      if (result.success) {
        toast.success("Đã xóa project thành công")
        router.push("/admin/projects")
      } else {
        toast.error(result.error || "Có lỗi xảy ra khi xóa project")
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra khi xóa project")
    } finally {
      setIsDeleting(false)
      setDeleteDialogOpen(false)
    }
  }

  const handleToggleFeatured = async () => {
    try {
      const result = await toggleProjectFeatured(project.id)
      if (result.success) {
        toast.success("Đã cập nhật trạng thái featured")
        router.refresh()
      } else {
        toast.error(result.error || "Có lỗi xảy ra")
      }
    } catch (error) {
      toast.error("Có lỗi xảy ra")
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" asChild>
            <Link href="/admin/projects">
              <RiArrowLeftLine className="mr-2 h-4 w-4" />
              Quay lại
            </Link>
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{project.name}</h1>
            <p className="text-muted-foreground">Chi tiết project</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" onClick={handleToggleFeatured}>
            {project.featuredOnHomepage ? (
              <RiStarFill className="mr-2 h-4 w-4 text-yellow-500" />
            ) : (
              <RiStarLine className="mr-2 h-4 w-4" />
            )}
            {project.featuredOnHomepage ? "Bỏ featured" : "Đánh dấu featured"}
          </Button>
          <Button variant="outline" size="sm" asChild>
            <Link href={`/admin/projects/${project.id}/edit`}>
              <RiEditLine className="mr-2 h-4 w-4" />
              Chỉnh sửa
            </Link>
          </Button>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => setDeleteDialogOpen(true)}
          >
            <RiDeleteBinLine className="mr-2 h-4 w-4" />
            Xóa
          </Button>
        </div>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {/* Project Info */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Thông tin cơ bản</CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-start gap-4">
              <div className="relative h-16 w-16 overflow-hidden rounded-lg border">
                <Image
                  src={project.logoUrl}
                  alt={`${project.name} logo`}
                  fill
                  className="object-contain"
                  sizes="64px"
                />
              </div>
              <div className="flex-1 space-y-2">
                <h3 className="text-lg font-semibold">{project.name}</h3>
                <p className="text-muted-foreground">{project.description}</p>
                <div className="flex items-center gap-2">
                  <Badge
                    variant={statusVariants[project.launchStatus as keyof typeof statusVariants]}
                  >
                    {statusLabels[project.launchStatus as keyof typeof statusLabels]}
                  </Badge>
                  {project.launchType && (
                    <Badge variant="outline">
                      {launchTypeLabels[project.launchType as keyof typeof launchTypeLabels]}
                    </Badge>
                  )}
                  {project.featuredOnHomepage && (
                    <Badge variant="default" className="bg-yellow-500">
                      Featured
                    </Badge>
                  )}
                </div>
              </div>
            </div>

            <Separator />

            <div className="grid gap-4 sm:grid-cols-2">
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <RiExternalLinkLine className="h-4 w-4" />
                  Website
                </div>
                <Link
                  href={project.websiteUrl}
                  target="_blank"
                  className="text-sm text-primary hover:underline"
                >
                  {project.websiteUrl}
                </Link>
              </div>

              {project.githubUrl && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <RiGithubLine className="h-4 w-4" />
                    GitHub
                  </div>
                  <Link
                    href={project.githubUrl}
                    target="_blank"
                    className="text-sm text-primary hover:underline"
                  >
                    {project.githubUrl}
                  </Link>
                </div>
              )}

              {project.twitterUrl && (
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <RiTwitterLine className="h-4 w-4" />
                    Twitter
                  </div>
                  <Link
                    href={project.twitterUrl}
                    target="_blank"
                    className="text-sm text-primary hover:underline"
                  >
                    {project.twitterUrl}
                  </Link>
                </div>
              )}

              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <RiMoneyDollarCircleLine className="h-4 w-4" />
                  Pricing
                </div>
                <div className="text-sm">
                  {pricingLabels[project.pricing as keyof typeof pricingLabels] || project.pricing}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Metadata */}
        <Card>
          <CardHeader>
            <CardTitle>Metadata</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <RiCalendarLine className="h-4 w-4" />
                Ngày tạo
              </div>
              <div className="text-sm text-muted-foreground">
                {new Date(project.createdAt).toLocaleString("vi-VN")}
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <RiCalendarLine className="h-4 w-4" />
                Cập nhật lần cuối
              </div>
              <div className="text-sm text-muted-foreground">
                {new Date(project.updatedAt).toLocaleString("vi-VN")}
              </div>
            </div>

            {project.scheduledLaunchDate && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <RiCalendarLine className="h-4 w-4" />
                  Ngày launch
                </div>
                <div className="text-sm text-muted-foreground">
                  {new Date(project.scheduledLaunchDate).toLocaleString("vi-VN")}
                </div>
              </div>
            )}

            {project.dailyRanking && (
              <div className="space-y-2">
                <div className="flex items-center gap-2 text-sm font-medium">
                  <RiStarLine className="h-4 w-4" />
                  Xếp hạng
                </div>
                <div className="text-sm text-muted-foreground">
                  #{project.dailyRanking}
                </div>
              </div>
            )}

            <div className="space-y-2">
              <div className="flex items-center gap-2 text-sm font-medium">
                <RiUserLine className="h-4 w-4" />
                Slug
              </div>
              <div className="text-sm text-muted-foreground font-mono">
                {project.slug}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Categories */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RiPriceTagLine className="h-4 w-4" />
              Danh mục
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {project.categories.map((category) => (
                <Badge key={category.id} variant="secondary">
                  {category.name}
                </Badge>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Tech Stack */}
        {project.techStack && project.techStack.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RiCodeLine className="h-4 w-4" />
                Tech Stack
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {project.techStack.map((tech) => (
                  <Badge key={tech} variant="outline">
                    {tech}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Platforms */}
        {project.platforms && project.platforms.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <RiSmartphoneLine className="h-4 w-4" />
                Platforms
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-2">
                {project.platforms.map((platform) => (
                  <Badge key={platform} variant="outline">
                    {platform}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Images */}
      {(project.coverImageUrl || project.productImage) && (
        <Card>
          <CardHeader>
            <CardTitle>Hình ảnh</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 sm:grid-cols-2">
              {project.coverImageUrl && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Cover Image</h4>
                  <div className="relative aspect-video overflow-hidden rounded-lg border">
                    <Image
                      src={project.coverImageUrl}
                      alt="Cover image"
                      fill
                      className="object-cover"
                      sizes="(max-width: 640px) 100vw, 50vw"
                    />
                  </div>
                </div>
              )}

              {project.productImage && (
                <div className="space-y-2">
                  <h4 className="text-sm font-medium">Product Image</h4>
                  <div className="relative aspect-video overflow-hidden rounded-lg border">
                    <Image
                      src={project.productImage}
                      alt="Product image"
                      fill
                      className="object-cover"
                      sizes="(max-width: 640px) 100vw, 50vw"
                    />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete confirmation dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Xác nhận xóa project</AlertDialogTitle>
            <AlertDialogDescription>
              Bạn có chắc chắn muốn xóa project "{project.name}"? Hành động này không thể hoàn tác.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Hủy</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteProject}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Đang xóa..." : "Xóa"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
