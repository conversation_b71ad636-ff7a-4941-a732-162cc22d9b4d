"use server"

import { revalidatePath } from "next/cache"
import { headers } from "next/headers"

import { db } from "@/drizzle/db"
import { category, project, projectToCategory, user } from "@/drizzle/db/schema"
import { addDays, format } from "date-fns"
import { and, desc, eq, gte, sql, like, or, inArray, count } from "drizzle-orm"

import { auth } from "@/lib/auth"
import { DATE_FORMAT, LAUNCH_SETTINGS } from "@/lib/constants"

import { getLaunchAvailabilityRange } from "./launch"

// Vérification des droits admin
async function checkAdminAccess() {
  const session = await auth.api.getSession({
    headers: await headers(),
  })
  if (!session?.user?.role || session.user.role !== "admin") {
    throw new Error("Unauthorized: Admin access required")
  }
}

// Get all users and launch stats
export async function getAdminStatsAndUsers() {
  await checkAdminAccess()

  // Get all users, sorted by registration date descending
  const usersData = await db.select().from(user).orderBy(desc(user.createdAt))

  // Get project counts for each user
  const projectCounts = await db
    .select({
      userId: project.createdBy,
      count: sql<number>`count(*)::int`,
    })
    .from(project)
    .where(sql`${project.createdBy} IS NOT NULL`)
    .groupBy(project.createdBy)

  // Create a map for quick lookup
  const projectCountMap = new Map(projectCounts.map((pc) => [pc.userId, pc.count]))

  // Combine user data with project counts
  const users = usersData.map((u) => ({
    ...u,
    hasLaunched: (projectCountMap.get(u.id) || 0) > 0,
    projectCount: projectCountMap.get(u.id) || 0,
  }))

  // Get today's date at midnight UTC
  const today = new Date()
  today.setUTCHours(0, 0, 0, 0)

  // Get new users today
  const newUsersToday = await db
    .select({ count: sql`count(*)` })
    .from(user)
    .where(gte(user.createdAt, today))

  // Get launch stats
  const totalLaunches = await db.select({ count: sql`count(*)` }).from(project)
  const premiumLaunches = await db
    .select({ count: sql`count(*)` })
    .from(project)
    .where(eq(project.launchType, "premium"))
  const premiumPlusLaunches = await db
    .select({ count: sql`count(*)` })
    .from(project)
    .where(eq(project.launchType, "premium_plus"))

  // Get new launches today
  const newLaunchesToday = await db
    .select({ count: sql`count(*)` })
    .from(project)
    .where(gte(project.createdAt, today))

  // Get new premium launches today
  const newPremiumLaunchesToday = await db
    .select({ count: sql`count(*)` })
    .from(project)
    .where(and(gte(project.createdAt, today), eq(project.launchType, "premium")))

  // Get new premium plus launches today
  const newPremiumPlusLaunchesToday = await db
    .select({ count: sql`count(*)` })
    .from(project)
    .where(and(gte(project.createdAt, today), eq(project.launchType, "premium_plus")))

  return {
    users,
    stats: {
      totalLaunches: Number(totalLaunches[0]?.count || 0),
      premiumLaunches: Number(premiumLaunches[0]?.count || 0),
      premiumPlusLaunches: Number(premiumPlusLaunches[0]?.count || 0),
      totalUsers: users.length,
      newUsersToday: Number(newUsersToday[0]?.count || 0),
      newLaunchesToday: Number(newLaunchesToday[0]?.count || 0),
      newPremiumLaunchesToday: Number(newPremiumLaunchesToday[0]?.count || 0),
      newPremiumPlusLaunchesToday: Number(newPremiumPlusLaunchesToday[0]?.count || 0),
    },
  }
}

// Get free launch availability
export async function getFreeLaunchAvailability() {
  await checkAdminAccess()

  const today = new Date()
  const startDate = format(addDays(today, LAUNCH_SETTINGS.MIN_DAYS_AHEAD), DATE_FORMAT.API)
  const endDate = format(addDays(today, LAUNCH_SETTINGS.MAX_DAYS_AHEAD), DATE_FORMAT.API)

  const availability = await getLaunchAvailabilityRange(startDate, endDate, "free")

  // Find the first available date
  const firstAvailableDate = availability.find((date) => date.freeSlots > 0)

  return {
    availability,
    firstAvailableDate: firstAvailableDate
      ? {
          date: firstAvailableDate.date,
          freeSlots: firstAvailableDate.freeSlots,
        }
      : null,
  }
}

// Get all categories
export async function getCategories() {
  await checkAdminAccess()

  const categories = await db
    .select({
      name: category.name,
    })
    .from(category)
    .orderBy(category.name)

  const totalCount = await db.select({ count: sql<number>`count(*)::int` }).from(category)

  return {
    categories,
    totalCount: totalCount[0]?.count || 0,
  }
}

// Add a new category
export async function addCategory(name: string) {
  await checkAdminAccess()

  // Name validation
  const trimmedName = name.trim()
  if (!trimmedName) {
    return { success: false, error: "Category name cannot be empty" }
  }
  if (trimmedName.length < 2) {
    return { success: false, error: "Category name must be at least 2 characters long" }
  }
  if (trimmedName.length > 50) {
    return { success: false, error: "Category name cannot exceed 50 characters" }
  }

  try {
    // Check if category already exists
    const existingCategory = await db
      .select()
      .from(category)
      .where(eq(category.name, trimmedName))
      .limit(1)

    if (existingCategory.length > 0) {
      return { success: false, error: "This category already exists" }
    }

    const id = trimmedName.toLowerCase().replace(/\s+/g, "-")

    await db.insert(category).values({
      id,
      name: trimmedName,
      createdAt: new Date(),
      updatedAt: new Date(),
    })
    return { success: true }
  } catch (error) {
    console.error("Error adding category:", error)
    if (error instanceof Error && error.message.includes("unique constraint")) {
      return { success: false, error: "This category already exists" }
    }
    return { success: false, error: "An error occurred while adding the category" }
  }
}

// ===== ADMIN PROJECTS MANAGEMENT =====

// Get all projects with pagination, search and filters
export async function getAdminProjects({
  page = 1,
  limit = 10,
  search = "",
  status = "",
  launchType = "",
  category = "",
  sortBy = "createdAt",
  sortOrder = "desc",
}: {
  page?: number
  limit?: number
  search?: string
  status?: string
  launchType?: string
  category?: string
  sortBy?: string
  sortOrder?: "asc" | "desc"
} = {}) {
  await checkAdminAccess()

  try {
    // Build where conditions
    const conditions = []

    // Search condition
    if (search) {
      conditions.push(
        or(
          like(project.name, `%${search}%`),
          like(project.description, `%${search}%`),
          like(project.websiteUrl, `%${search}%`)
        )
      )
    }

    // Status filter
    if (status) {
      conditions.push(eq(project.launchStatus, status))
    }

    // Launch type filter
    if (launchType) {
      conditions.push(eq(project.launchType, launchType))
    }

    // Category filter
    if (category) {
      const projectsInCategory = await db
        .select({ projectId: projectToCategory.projectId })
        .from(projectToCategory)
        .where(eq(projectToCategory.categoryId, category))

      if (projectsInCategory.length > 0) {
        conditions.push(
          inArray(
            project.id,
            projectsInCategory.map((p) => p.projectId)
          )
        )
      } else {
        // No projects in this category
        return {
          projects: [],
          totalCount: 0,
          totalPages: 0,
          currentPage: page,
        }
      }
    }

    // Combine conditions
    const whereClause = conditions.length > 0 ? and(...conditions) : undefined

    // Get total count
    const totalCountResult = await db
      .select({ count: count() })
      .from(project)
      .where(whereClause)

    const totalCount = totalCountResult[0]?.count || 0
    const totalPages = Math.ceil(totalCount / limit)
    const offset = (page - 1) * limit

    // Get projects with pagination
    const projectsQuery = db
      .select({
        id: project.id,
        name: project.name,
        slug: project.slug,
        description: project.description,
        websiteUrl: project.websiteUrl,
        logoUrl: project.logoUrl,
        coverImageUrl: project.coverImageUrl,
        productImage: project.productImage,
        githubUrl: project.githubUrl,
        twitterUrl: project.twitterUrl,
        techStack: project.techStack,
        pricing: project.pricing,
        platforms: project.platforms,
        launchStatus: project.launchStatus,
        scheduledLaunchDate: project.scheduledLaunchDate,
        launchType: project.launchType,
        featuredOnHomepage: project.featuredOnHomepage,
        dailyRanking: project.dailyRanking,
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
        createdBy: project.createdBy,
      })
      .from(project)
      .where(whereClause)
      .limit(limit)
      .offset(offset)

    // Apply sorting
    if (sortBy === "createdAt") {
      projectsQuery.orderBy(sortOrder === "asc" ? project.createdAt : desc(project.createdAt))
    } else if (sortBy === "name") {
      projectsQuery.orderBy(sortOrder === "asc" ? project.name : desc(project.name))
    } else if (sortBy === "launchStatus") {
      projectsQuery.orderBy(sortOrder === "asc" ? project.launchStatus : desc(project.launchStatus))
    } else if (sortBy === "dailyRanking") {
      projectsQuery.orderBy(sortOrder === "asc" ? project.dailyRanking : desc(project.dailyRanking))
    }

    const projects = await projectsQuery

    // Get categories for each project
    const projectsWithCategories = await Promise.all(
      projects.map(async (proj) => {
        const categories = await db
          .select({
            id: category.id,
            name: category.name,
          })
          .from(category)
          .innerJoin(projectToCategory, eq(category.id, projectToCategory.categoryId))
          .where(eq(projectToCategory.projectId, proj.id))

        return {
          ...proj,
          categories,
        }
      })
    )

    return {
      projects: projectsWithCategories,
      totalCount,
      totalPages,
      currentPage: page,
    }
  } catch (error) {
    console.error("Error fetching admin projects:", error)
    throw new Error("Failed to fetch projects")
  }
}

// Get a single project by ID
export async function getAdminProjectById(projectId: string) {
  await checkAdminAccess()

  try {
    const [projectData] = await db
      .select({
        id: project.id,
        name: project.name,
        slug: project.slug,
        description: project.description,
        websiteUrl: project.websiteUrl,
        logoUrl: project.logoUrl,
        coverImageUrl: project.coverImageUrl,
        productImage: project.productImage,
        githubUrl: project.githubUrl,
        twitterUrl: project.twitterUrl,
        techStack: project.techStack,
        pricing: project.pricing,
        platforms: project.platforms,
        launchStatus: project.launchStatus,
        scheduledLaunchDate: project.scheduledLaunchDate,
        launchType: project.launchType,
        featuredOnHomepage: project.featuredOnHomepage,
        dailyRanking: project.dailyRanking,
        createdAt: project.createdAt,
        updatedAt: project.updatedAt,
        createdBy: project.createdBy,
      })
      .from(project)
      .where(eq(project.id, projectId))

    if (!projectData) {
      return { success: false, error: "Project not found" }
    }

    // Get categories for the project
    const categories = await db
      .select({
        id: category.id,
        name: category.name,
      })
      .from(category)
      .innerJoin(projectToCategory, eq(category.id, projectToCategory.categoryId))
      .where(eq(projectToCategory.projectId, projectId))

    return {
      success: true,
      project: {
        ...projectData,
        categories,
      },
    }
  } catch (error) {
    console.error("Error fetching project:", error)
    return { success: false, error: "Failed to fetch project" }
  }
}

// Update a project
export async function updateAdminProject(
  projectId: string,
  projectData: {
    name?: string
    description?: string
    websiteUrl?: string
    logoUrl?: string
    coverImageUrl?: string | null
    productImage?: string | null
    githubUrl?: string | null
    twitterUrl?: string | null
    techStack?: string[]
    pricing?: string
    platforms?: string[]
    launchStatus?: string
    scheduledLaunchDate?: Date | null
    launchType?: string
    featuredOnHomepage?: boolean
    categories?: string[]
  }
) {
  await checkAdminAccess()

  try {
    // Update project data
    await db
      .update(project)
      .set({
        ...(projectData.name && { name: projectData.name }),
        ...(projectData.description && { description: projectData.description }),
        ...(projectData.websiteUrl && { websiteUrl: projectData.websiteUrl }),
        ...(projectData.logoUrl && { logoUrl: projectData.logoUrl }),
        ...(projectData.coverImageUrl !== undefined && { coverImageUrl: projectData.coverImageUrl }),
        ...(projectData.productImage !== undefined && { productImage: projectData.productImage }),
        ...(projectData.githubUrl !== undefined && { githubUrl: projectData.githubUrl }),
        ...(projectData.twitterUrl !== undefined && { twitterUrl: projectData.twitterUrl }),
        ...(projectData.techStack && { techStack: projectData.techStack }),
        ...(projectData.pricing && { pricing: projectData.pricing }),
        ...(projectData.platforms && { platforms: projectData.platforms }),
        ...(projectData.launchStatus && { launchStatus: projectData.launchStatus }),
        ...(projectData.scheduledLaunchDate !== undefined && {
          scheduledLaunchDate: projectData.scheduledLaunchDate,
        }),
        ...(projectData.launchType && { launchType: projectData.launchType }),
        ...(projectData.featuredOnHomepage !== undefined && {
          featuredOnHomepage: projectData.featuredOnHomepage,
        }),
        updatedAt: new Date(),
      })
      .where(eq(project.id, projectId))

    // Update categories if provided
    if (projectData.categories) {
      // Delete existing categories
      await db.delete(projectToCategory).where(eq(projectToCategory.projectId, projectId))

      // Add new categories
      if (projectData.categories.length > 0) {
        await db.insert(projectToCategory).values(
          projectData.categories.map((categoryId) => ({
            projectId,
            categoryId,
          }))
        )
      }
    }

    revalidatePath("/admin/projects")
    revalidatePath(`/admin/projects/${projectId}`)
    revalidatePath("/")

    return { success: true }
  } catch (error) {
    console.error("Error updating project:", error)
    return { success: false, error: "Failed to update project" }
  }
}

// Delete a project
export async function deleteAdminProject(projectId: string) {
  await checkAdminAccess()

  try {
    // Delete project (categories will be deleted automatically due to cascade)
    await db.delete(project).where(eq(project.id, projectId))

    revalidatePath("/admin/projects")
    revalidatePath("/")

    return { success: true }
  } catch (error) {
    console.error("Error deleting project:", error)
    return { success: false, error: "Failed to delete project" }
  }
}

// Bulk update projects
export async function bulkUpdateProjects(
  projectIds: string[],
  updates: {
    launchStatus?: string
    featuredOnHomepage?: boolean
    launchType?: string
  }
) {
  await checkAdminAccess()

  try {
    await db
      .update(project)
      .set({
        ...(updates.launchStatus && { launchStatus: updates.launchStatus }),
        ...(updates.featuredOnHomepage !== undefined && {
          featuredOnHomepage: updates.featuredOnHomepage,
        }),
        ...(updates.launchType && { launchType: updates.launchType }),
        updatedAt: new Date(),
      })
      .where(inArray(project.id, projectIds))

    revalidatePath("/admin/projects")
    revalidatePath("/")

    return { success: true }
  } catch (error) {
    console.error("Error bulk updating projects:", error)
    return { success: false, error: "Failed to update projects" }
  }
}

// Bulk delete projects
export async function bulkDeleteProjects(projectIds: string[]) {
  await checkAdminAccess()

  try {
    await db.delete(project).where(inArray(project.id, projectIds))

    revalidatePath("/admin/projects")
    revalidatePath("/")

    return { success: true }
  } catch (error) {
    console.error("Error bulk deleting projects:", error)
    return { success: false, error: "Failed to delete projects" }
  }
}

// Toggle project featured status
export async function toggleProjectFeatured(projectId: string) {
  await checkAdminAccess()

  try {
    const [currentProject] = await db
      .select({ featuredOnHomepage: project.featuredOnHomepage })
      .from(project)
      .where(eq(project.id, projectId))

    if (!currentProject) {
      return { success: false, error: "Project not found" }
    }

    await db
      .update(project)
      .set({
        featuredOnHomepage: !currentProject.featuredOnHomepage,
        updatedAt: new Date(),
      })
      .where(eq(project.id, projectId))

    revalidatePath("/admin/projects")
    revalidatePath("/")

    return { success: true }
  } catch (error) {
    console.error("Error toggling project featured status:", error)
    return { success: false, error: "Failed to update project" }
  }
}
