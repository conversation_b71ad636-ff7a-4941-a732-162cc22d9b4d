"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"

import {
  RiDashboardLine,
  RiRocketLine,
  RiUserLine,
  RiSettings3Line,
  RiBarChartLine,
  RiPriceTagLine,
  RiMenuLine,
  RiCloseLine,
} from "@remixicon/react"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { Separator } from "@/components/ui/separator"
import { Badge } from "@/components/ui/badge"

const navigation = [
  {
    name: "Dashboard",
    href: "/admin",
    icon: RiDashboardLine,
    description: "Tổng quan hệ thống",
  },
  {
    name: "Projects",
    href: "/admin/projects",
    icon: RiRocketLine,
    description: "Quản lý dự án",
    badge: "New",
  },
  {
    name: "Users",
    href: "/admin/users",
    icon: RiUserL<PERSON>,
    description: "<PERSON>u<PERSON>n lý người dùng",
  },
  {
    name: "Categories",
    href: "/admin/categories",
    icon: RiPriceTagLine,
    description: "Quản lý danh mục",
  },
  {
    name: "Analytics",
    href: "/admin/analytics",
    icon: RiBarChartLine,
    description: "Thống kê & báo cáo",
  },
  {
    name: "Settings",
    href: "/admin/settings",
    icon: RiSettings3Line,
    description: "Cài đặt hệ thống",
  },
]

interface AdminSidebarProps {
  className?: string
}

function SidebarContent({ onNavigate }: { onNavigate?: () => void }) {
  const pathname = usePathname()

  return (
    <div className="flex h-full flex-col">
      {/* Header */}
      <div className="flex h-16 items-center border-b px-6">
        <Link href="/admin" className="flex items-center gap-2">
          <div className="flex h-8 w-8 items-center justify-center rounded-lg bg-primary text-primary-foreground">
            <RiSettings3Line className="h-4 w-4" />
          </div>
          <div>
            <h1 className="text-lg font-bold">Admin Panel</h1>
            <p className="text-xs text-muted-foreground">Open Launch</p>
          </div>
        </Link>
      </div>

      {/* Navigation */}
      <nav className="flex-1 space-y-1 p-4">
        {navigation.map((item) => {
          const isActive = pathname === item.href || (item.href !== "/admin" && pathname.startsWith(item.href))
          
          return (
            <Link
              key={item.name}
              href={item.href}
              onClick={onNavigate}
              className={cn(
                "flex items-center gap-3 rounded-lg px-3 py-2 text-sm font-medium transition-colors",
                "hover:bg-accent hover:text-accent-foreground",
                isActive
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "text-muted-foreground"
              )}
            >
              <item.icon className="h-4 w-4 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2">
                  <span className="truncate">{item.name}</span>
                  {item.badge && (
                    <Badge variant="secondary" className="text-xs">
                      {item.badge}
                    </Badge>
                  )}
                </div>
                <p className="text-xs opacity-75 truncate">{item.description}</p>
              </div>
            </Link>
          )
        })}
      </nav>

      <Separator />

      {/* Footer */}
      <div className="p-4">
        <div className="rounded-lg bg-muted/50 p-3">
          <h3 className="text-sm font-medium">Cần hỗ trợ?</h3>
          <p className="text-xs text-muted-foreground mt-1">
            Liên hệ team phát triển để được hỗ trợ
          </p>
          <Button variant="outline" size="sm" className="mt-2 w-full">
            Liên hệ hỗ trợ
          </Button>
        </div>
      </div>
    </div>
  )
}

export function AdminSidebar({ className }: AdminSidebarProps) {
  return (
    <>
      {/* Desktop Sidebar */}
      <aside className={cn("hidden w-64 border-r bg-card lg:block", className)}>
        <SidebarContent />
      </aside>

      {/* Mobile Sidebar */}
      <div className="lg:hidden">
        <Sheet>
          <SheetTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="fixed left-4 top-4 z-50 lg:hidden"
              aria-label="Mở menu"
            >
              <RiMenuLine className="h-5 w-5" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="w-64 p-0">
            <SidebarContent onNavigate={() => {}} />
          </SheetContent>
        </Sheet>
      </div>
    </>
  )
}
