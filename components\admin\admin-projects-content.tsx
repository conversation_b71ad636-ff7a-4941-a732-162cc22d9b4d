import { getAdminProjects } from "@/app/actions/admin"
import { getCategories } from "@/app/actions/projects"
import { AdminProjectsTable } from "./admin-projects-table"
import { AdminProjectsFilters } from "./admin-projects-filters"

interface AdminProjectsContentProps {
  searchParams: Promise<{
    page?: string
    search?: string
    status?: string
    launchType?: string
    category?: string
    sortBy?: string
    sortOrder?: string
  }>
}

export async function AdminProjectsContent({ searchParams }: AdminProjectsContentProps) {
  const params = await searchParams
  
  const page = parseInt(params.page || "1")
  const search = params.search || ""
  const status = params.status || ""
  const launchType = params.launchType || ""
  const category = params.category || ""
  const sortBy = params.sortBy || "createdAt"
  const sortOrder = (params.sortOrder as "asc" | "desc") || "desc"

  // Fetch projects and categories in parallel
  const [projectsData, categoriesData] = await Promise.all([
    getAdminProjects({
      page,
      limit: 10,
      search,
      status,
      launchType,
      category,
      sortBy,
      sortOrder,
    }),
    getCategories(),
  ])

  return (
    <div className="space-y-6">
      <AdminProjectsFilters
        categories={categoriesData}
        currentFilters={{
          search,
          status,
          launchType,
          category,
          sortBy,
          sortOrder,
        }}
      />
      
      <AdminProjectsTable
        projects={projectsData.projects}
        totalCount={projectsData.totalCount}
        totalPages={projectsData.totalPages}
        currentPage={projectsData.currentPage}
        currentFilters={{
          search,
          status,
          launchType,
          category,
          sortBy,
          sortOrder,
        }}
      />
    </div>
  )
}
