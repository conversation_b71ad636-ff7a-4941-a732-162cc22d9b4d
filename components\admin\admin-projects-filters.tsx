"use client"

import { useRout<PERSON>, useSearchParams } from "next/navigation"
import { useState, useTransition } from "react"

import {
  RiSearchLine,
  RiFilterLine,
  RiCloseLine,
  RiArrowUpDownLine,
} from "@remixicon/react"

import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu"

interface Category {
  id: string
  name: string
}

interface AdminProjectsFiltersProps {
  categories: Category[]
  currentFilters: {
    search: string
    status: string
    launchType: string
    category: string
    sortBy: string
    sortOrder: string
  }
}

const statusOptions = [
  { value: "", label: "Tất cả trạng thái" },
  { value: "scheduled", label: "Đã lên lịch" },
  { value: "launched", label: "Đã ra mắt" },
  { value: "draft", label: "Bản nháp" },
  { value: "cancelled", label: "Đã hủy" },
]

const launchTypeOptions = [
  { value: "", label: "Tất cả loại launch" },
  { value: "free", label: "Miễn phí" },
  { value: "premium", label: "Premium" },
  { value: "premium_plus", label: "Premium Plus" },
]

const sortOptions = [
  { value: "createdAt", label: "Ngày tạo" },
  { value: "name", label: "Tên project" },
  { value: "launchStatus", label: "Trạng thái" },
  { value: "dailyRanking", label: "Xếp hạng" },
]

export function AdminProjectsFilters({ categories, currentFilters }: AdminProjectsFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isPending, startTransition] = useTransition()
  const [searchValue, setSearchValue] = useState(currentFilters.search)

  const updateFilters = (updates: Record<string, string>) => {
    const params = new URLSearchParams(searchParams.toString())
    
    Object.entries(updates).forEach(([key, value]) => {
      if (value) {
        params.set(key, value)
      } else {
        params.delete(key)
      }
    })
    
    // Reset to page 1 when filters change
    params.delete("page")
    
    startTransition(() => {
      router.push(`/admin/projects?${params.toString()}`)
    })
  }

  const clearFilters = () => {
    setSearchValue("")
    startTransition(() => {
      router.push("/admin/projects")
    })
  }

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    updateFilters({ search: searchValue })
  }

  const activeFiltersCount = [
    currentFilters.search,
    currentFilters.status,
    currentFilters.launchType,
    currentFilters.category,
  ].filter(Boolean).length

  return (
    <div className="space-y-4">
      {/* Search and primary filters */}
      <div className="flex flex-col gap-4 md:flex-row md:items-center">
        {/* Search */}
        <form onSubmit={handleSearch} className="flex-1 max-w-sm">
          <div className="relative">
            <RiSearchLine className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
            <Input
              placeholder="Tìm kiếm projects..."
              value={searchValue}
              onChange={(e) => setSearchValue(e.target.value)}
              className="pl-9"
            />
          </div>
        </form>

        {/* Filters */}
        <div className="flex items-center gap-2">
          <Select
            value={currentFilters.status}
            onValueChange={(value) => updateFilters({ status: value })}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Trạng thái" />
            </SelectTrigger>
            <SelectContent>
              {statusOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={currentFilters.launchType}
            onValueChange={(value) => updateFilters({ launchType: value })}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Loại launch" />
            </SelectTrigger>
            <SelectContent>
              {launchTypeOptions.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={currentFilters.category}
            onValueChange={(value) => updateFilters({ category: value })}
          >
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Danh mục" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="">Tất cả danh mục</SelectItem>
              {categories.map((category) => (
                <SelectItem key={category.id} value={category.id}>
                  {category.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Sort */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="gap-2">
                <RiArrowUpDownLine className="h-4 w-4" />
                Sắp xếp
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {sortOptions.map((option) => (
                <div key={option.value}>
                  <DropdownMenuItem
                    onClick={() =>
                      updateFilters({
                        sortBy: option.value,
                        sortOrder: "desc",
                      })
                    }
                  >
                    {option.label} (Giảm dần)
                  </DropdownMenuItem>
                  <DropdownMenuItem
                    onClick={() =>
                      updateFilters({
                        sortBy: option.value,
                        sortOrder: "asc",
                      })
                    }
                  >
                    {option.label} (Tăng dần)
                  </DropdownMenuItem>
                </div>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>

      {/* Active filters */}
      {activeFiltersCount > 0 && (
        <div className="flex items-center gap-2">
          <span className="text-sm text-muted-foreground">Bộ lọc đang áp dụng:</span>
          
          {currentFilters.search && (
            <Badge variant="secondary" className="gap-1">
              Tìm kiếm: {currentFilters.search}
              <button
                onClick={() => {
                  setSearchValue("")
                  updateFilters({ search: "" })
                }}
                className="ml-1 hover:bg-muted-foreground/20 rounded-sm"
              >
                <RiCloseLine className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {currentFilters.status && (
            <Badge variant="secondary" className="gap-1">
              Trạng thái: {statusOptions.find(o => o.value === currentFilters.status)?.label}
              <button
                onClick={() => updateFilters({ status: "" })}
                className="ml-1 hover:bg-muted-foreground/20 rounded-sm"
              >
                <RiCloseLine className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {currentFilters.launchType && (
            <Badge variant="secondary" className="gap-1">
              Launch: {launchTypeOptions.find(o => o.value === currentFilters.launchType)?.label}
              <button
                onClick={() => updateFilters({ launchType: "" })}
                className="ml-1 hover:bg-muted-foreground/20 rounded-sm"
              >
                <RiCloseLine className="h-3 w-3" />
              </button>
            </Badge>
          )}
          
          {currentFilters.category && (
            <Badge variant="secondary" className="gap-1">
              Danh mục: {categories.find(c => c.id === currentFilters.category)?.name}
              <button
                onClick={() => updateFilters({ category: "" })}
                className="ml-1 hover:bg-muted-foreground/20 rounded-sm"
              >
                <RiCloseLine className="h-3 w-3" />
              </button>
            </Badge>
          )}

          <Button
            variant="ghost"
            size="sm"
            onClick={clearFilters}
            className="gap-1 text-muted-foreground hover:text-foreground"
          >
            <RiCloseLine className="h-4 w-4" />
            Xóa tất cả
          </Button>
        </div>
      )}
    </div>
  )
}
